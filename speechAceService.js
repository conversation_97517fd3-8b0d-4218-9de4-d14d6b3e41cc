import axios from "axios";
import FormData from "form-data";
import dotenv from "dotenv";
import ffmpeg from "fluent-ffmpeg";
import path from "path";
import fs from "fs";
import { Buffer } from "buffer";
import process from "process";
import {
  calculateTextSimilarity,
  cosineSimilarity,
  getVector,
} from "./utils/textSimilarityUtils.js";

dotenv.config();

// Constants for API configuration
const API_CONFIG = {
  BASE_URL: "https://api5.speechace.com/api/scoring/speech/v9/json",
  KEY:
    process.env.SPEECH_ACE_API_KEY ||
    "cYXTjG4VyxvSGuzPwHESA9Gy6DLmF0dinm%2B87Her33qxfjwYWWdsh0KI8JBxiKp%2BGC2yJlsJQZkJbw0VTdJZA%2Fnia1gzhdBDxiu3OMMwujSkZNk90%2Fd1igK2OSv5ACcj",
  TIMEOUT: 120000, // Increased timeout to 2 minutes
  MAX_RETRIES: 3, // Increased retries
  RETRY_DELAY: 3000, // Increased delay between retries
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB max file size
  COMPRESSION_THRESHOLD: 2 * 1024 * 1024, // 2MB threshold for compression
};

// Helper function to create temporary file
function createTempFile(buffer, extension) {
  const tempDir = process.env.TEMP || process.env.TMP || "/tmp";
  const tempPath = path.join(
    tempDir,
    `audio_${Date.now()}_${Math.random()
      .toString(36)
      .substring(7)}.${extension}`
  );
  fs.writeFileSync(tempPath, buffer);
  return tempPath;
}

// Helper function to clean up temporary files
function cleanupTempFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up temp file: ${filePath}`);
    }
  } catch (error) {
    console.warn(`Failed to cleanup temp file ${filePath}:`, error.message);
  }
}

// Enhanced audio compression function using FFmpeg
async function compressAudioWithFFmpeg(audioBuffer) {
  let inputPath = null;
  let outputPath = null;

  try {
    console.log(
      `Starting audio compression. Original size: ${(
        audioBuffer.length /
        1024 /
        1024
      ).toFixed(2)}MB`
    );

    // Create temporary input file
    inputPath = createTempFile(audioBuffer, "wav");
    outputPath = inputPath.replace(".wav", "_compressed.mp3");

    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .audioCodec("libmp3lame")
        .audioBitrate("64k") // Lower bitrate for smaller file size
        .audioFrequency(16000) // 16kHz sample rate for speech
        .audioChannels(1) // Mono
        .format("mp3")
        .on("start", (commandLine) => {
          console.log("FFmpeg compression started:", commandLine);
        })
        .on("progress", (progress) => {
          console.log(`Compression progress: ${progress.percent}%`);
        })
        .on("end", () => {
          try {
            const compressedBuffer = fs.readFileSync(outputPath);
            console.log(
              `Compression completed. New size: ${(
                compressedBuffer.length /
                1024 /
                1024
              ).toFixed(2)}MB`
            );

            // Cleanup temp files
            cleanupTempFile(inputPath);
            cleanupTempFile(outputPath);

            resolve(compressedBuffer);
          } catch (readError) {
            reject(
              new Error(`Failed to read compressed file: ${readError.message}`)
            );
          }
        })
        .on("error", (err) => {
          console.error("FFmpeg compression error:", err);

          // Cleanup temp files on error
          cleanupTempFile(inputPath);
          cleanupTempFile(outputPath);

          reject(new Error(`Audio compression failed: ${err.message}`));
        })
        .save(outputPath);
    });
  } catch (error) {
    // Cleanup temp files on error
    if (inputPath) cleanupTempFile(inputPath);
    if (outputPath) cleanupTempFile(outputPath);

    throw new Error(`Audio compression setup failed: ${error.message}`);
  }
}

// Alternative lightweight compression for smaller files
async function lightweightCompress(audioBuffer) {
  try {
    console.log(
      `Applying lightweight compression. Original size: ${(
        audioBuffer.length /
        1024 /
        1024
      ).toFixed(2)}MB`
    );

    // Simple compression by reducing sample rate and bit depth
    // This is a basic implementation - you might want to use a more sophisticated approach
    const compressionRatio = 0.7; // Reduce to 70% of original size
    const targetSize = Math.floor(audioBuffer.length * compressionRatio);
    const step = Math.floor(audioBuffer.length / targetSize);

    const compressedBuffer = Buffer.alloc(targetSize);
    for (let i = 0; i < targetSize; i++) {
      compressedBuffer[i] = audioBuffer[i * step];
    }

    console.log(
      `Lightweight compression completed. New size: ${(
        compressedBuffer.length /
        1024 /
        1024
      ).toFixed(2)}MB`
    );
    return compressedBuffer;
  } catch (error) {
    console.warn("Lightweight compression failed:", error.message);
    return audioBuffer; // Return original if compression fails
  }
}

// Enhanced audio optimization function
async function optimizeAudioForApi(audioBuffer) {
  try {
    const originalSize = audioBuffer.length;
    const sizeInMB = originalSize / 1024 / 1024;

    console.log(
      `Audio optimization started. Original size: ${sizeInMB.toFixed(2)}MB`
    );

    // Check if file is too large
    if (originalSize > API_CONFIG.MAX_FILE_SIZE) {
      throw new Error(
        `Audio file too large: ${sizeInMB.toFixed(2)}MB. Maximum allowed: ${
          API_CONFIG.MAX_FILE_SIZE / 1024 / 1024
        }MB`
      );
    }

    // Return original if under compression threshold
    if (originalSize < API_CONFIG.COMPRESSION_THRESHOLD) {
      console.log("Audio size acceptable, no compression needed");
      return audioBuffer;
    }

    // Environment detection for compression strategy
    const isVercel = process.env.VERCEL;
    const isRailway = process.env.RAILWAY_ENVIRONMENT;
    const isAWSLambda = process.env.AWS_LAMBDA_FUNCTION_NAME;
    const isServerless = isVercel || isAWSLambda;

    // Railway supports FFmpeg, so prefer it on Railway
    if (isRailway) {
      console.log(
        "Railway environment detected, preferring FFmpeg compression"
      );
      try {
        return await compressAudioWithFFmpeg(audioBuffer);
      } catch (ffmpegError) {
        console.warn(
          "FFmpeg compression failed on Railway, trying lightweight compression:",
          ffmpegError.message
        );
        try {
          return await lightweightCompress(audioBuffer);
        } catch (lightweightError) {
          console.warn(
            "Lightweight compression failed, using original:",
            lightweightError.message
          );
          return audioBuffer;
        }
      }
    }

    // For serverless environments (Vercel, AWS Lambda), skip FFmpeg
    if (isServerless) {
      console.log(
        "Serverless environment detected, using lightweight compression only"
      );
      try {
        return await lightweightCompress(audioBuffer);
      } catch (lightweightError) {
        console.warn(
          "Lightweight compression failed, using original:",
          lightweightError.message
        );
        return audioBuffer;
      }
    }

    // Try FFmpeg compression first (only in non-serverless environments)
    try {
      return await compressAudioWithFFmpeg(audioBuffer);
    } catch (ffmpegError) {
      console.warn(
        "FFmpeg compression failed, trying lightweight compression:",
        ffmpegError.message
      );

      // Fallback to lightweight compression
      try {
        return await lightweightCompress(audioBuffer);
      } catch (lightweightError) {
        console.warn(
          "Lightweight compression failed, using original:",
          lightweightError.message
        );
        return audioBuffer;
      }
    }
  } catch (error) {
    console.error("Audio optimization failed:", error);
    throw error;
  }
}

// Enhanced API request function with better retry logic
async function makeApiRequestWithRetry(
  url,
  formData,
  config,
  retries = API_CONFIG.MAX_RETRIES
) {
  let lastError;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      console.log(`API request attempt ${attempt + 1}/${retries + 1}`);

      const response = await axios.post(url, formData, {
        ...config,
        timeout: API_CONFIG.TIMEOUT,
      });

      console.log(`API request successful on attempt ${attempt + 1}`);
      return response;
    } catch (error) {
      lastError = error;

      // Don't retry on 4xx errors (client errors)
      if (
        error.response &&
        error.response.status >= 400 &&
        error.response.status < 500
      ) {
        console.log(`Client error (${error.response.status}), not retrying`);
        throw error;
      }

      // Don't retry on the last attempt
      if (attempt === retries) {
        break;
      }

      const delay = API_CONFIG.RETRY_DELAY * (attempt + 1); // Exponential backoff
      console.log(
        `API request failed (attempt ${attempt + 1}). Retrying in ${
          delay / 1000
        }s...`
      );
      console.log(`Error: ${error.message}`);

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // All retries exhausted
  console.error(`All ${retries + 1} API request attempts failed`);
  throw lastError;
}

// Validate audio buffer before processing
function validateAudioBuffer(audioBuffer, context = "audio processing") {
  if (!audioBuffer || !Buffer.isBuffer(audioBuffer)) {
    throw new Error(`Invalid audio buffer provided for ${context}`);
  }

  if (audioBuffer.length === 0) {
    throw new Error(`Empty audio buffer provided for ${context}`);
  }

  if (audioBuffer.length > API_CONFIG.MAX_FILE_SIZE) {
    const sizeInMB = (audioBuffer.length / 1024 / 1024).toFixed(2);
    const maxSizeInMB = (API_CONFIG.MAX_FILE_SIZE / 1024 / 1024).toFixed(2);
    throw new Error(
      `Audio file too large: ${sizeInMB}MB. Maximum allowed: ${maxSizeInMB}MB`
    );
  }

  console.log(
    `Audio validation passed for ${context}. Size: ${(
      audioBuffer.length /
      1024 /
      1024
    ).toFixed(2)}MB`
  );
}

// Optimized read-aloud analysis function
export const analyzeSpeech = async (audioBuffer, text) => {
  try {
    // Validate inputs
    validateAudioBuffer(audioBuffer, "speech analysis");

    if (!text || typeof text !== "string" || text.trim().length === 0) {
      throw new Error(
        "Text for speech analysis is required and cannot be empty"
      );
    }

    const apiKey = API_CONFIG.KEY;
    if (!apiKey) {
      throw new Error("Speech Ace API key is not configured");
    }

    const userId = `user_${Math.floor(Math.random() * 10000)}`;
    const url = `${API_CONFIG.BASE_URL}?key=${apiKey}&user_id=${userId}&dialect=en-us`;

    console.log("Starting speech analysis:", {
      textLength: text.length,
      originalAudioSize: `${(audioBuffer.length / 1024 / 1024).toFixed(2)}MB`,
      userId: userId,
    });

    // Optimize audio before sending
    const optimizedAudio = await optimizeAudioForApi(audioBuffer);

    const formData = new FormData();
    formData.append("text", text.trim());
    formData.append("user_audio_file", optimizedAudio, {
      filename: "recording.wav",
      contentType: "audio/wav",
    });
    formData.append("question_info", "u1/q1");

    console.log("Making request to SpeechAce API:", {
      optimizedAudioSize: `${(optimizedAudio.length / 1024 / 1024).toFixed(
        2
      )}MB`,
      compressionRatio: `${(
        (1 - optimizedAudio.length / audioBuffer.length) *
        100
      ).toFixed(1)}%`,
    });

    // Use enhanced retry logic
    const response = await makeApiRequestWithRetry(url, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: "application/json",
      },
      maxBodyLength: Infinity,
    });

    if (response.data.status === "error") {
      throw new Error(
        response.data.detail_message ||
          response.data.short_message ||
          "SpeechAce API returned an error"
      );
    }

    console.log("SpeechAce API Response received successfully");

    // Extract transcript and provided text
    const transcript = response.data.speech_score?.transcript || "";
    const providedText = text.trim();

    // Handle empty transcript
    if (!transcript || transcript.trim().length === 0) {
      console.warn("Empty transcript received from SpeechAce API");
      response.data.content = {
        score: 0,
        transcript: "",
        provided_text: providedText,
        similarity_score: 0,
        similarity_details: {
          cosine: 0,
          jaccard: 0,
          overlapPercentage: 0,
          matchedWords: [],
        },
      };
      return response.data;
    }

    // Use enhanced similarity calculation
    const similarityResult = calculateTextSimilarity(providedText, transcript);

    // Scale the combined similarity score to out of 90
    const contentScore = Math.round(similarityResult.combinedScore * 90);

    // Add detailed content field to the response
    response.data.content = {
      score: contentScore,
      transcript: transcript,
      provided_text: providedText,
      similarity_score: similarityResult.combinedScore,
      similarity_details: {
        cosine: similarityResult.cosine,
        jaccard: similarityResult.jaccard,
        overlapPercentage: similarityResult.overlapPercentage,
        matchedWords: similarityResult.matchedWords,
        uniqueWordsInProvidedText: similarityResult.totalUniqueWords1,
        uniqueWordsInTranscript: similarityResult.totalUniqueWords2,
      },
    };

    console.log("Speech analysis completed successfully:", {
      contentScore,
      similarityScore: similarityResult.combinedScore,
      transcriptLength: transcript.length,
    });

    return response.data;
  } catch (error) {
    console.error("Speech analysis error:", {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });

    // Provide specific error messages based on error type
    if (error.code === "ECONNABORTED") {
      throw new Error(
        "The speech analysis request timed out. Your audio might be too long or the server is busy. Please try again with a shorter recording."
      );
    }

    if (error.message.includes("network") || error.code === "ENOTFOUND") {
      throw new Error(
        "Network error occurred. Please check your internet connection and try again."
      );
    }

    if (error.response?.status === 413) {
      throw new Error(
        "Audio file is too large. Please record a shorter audio or check your microphone settings."
      );
    }

    if (error.response?.status === 429) {
      throw new Error(
        "Too many requests. Please wait a moment before trying again."
      );
    }

    if (error.response?.data?.status === "error") {
      throw new Error(
        error.response.data.detail_message ||
          error.response.data.short_message ||
          "SpeechAce API error occurred"
      );
    }

    // Re-throw with original message if no specific handling
    throw error;
  }
};

// Enhanced PTE task analysis function
export const analyzePTETask = async (audioBuffer, taskType, context = "") => {
  try {
    // Validate inputs
    validateAudioBuffer(audioBuffer, `PTE ${taskType} analysis`);

    const validTaskTypes = [
      "describe-image",
      "retell-lecture",
      "answer-question",
    ];
    if (!validTaskTypes.includes(taskType)) {
      throw new Error(
        `Invalid task type: ${taskType}. Must be one of: ${validTaskTypes.join(
          ", "
        )}`
      );
    }

    const apiKey = API_CONFIG.KEY;
    if (!apiKey) {
      throw new Error("Speech Ace API key is not configured");
    }

    const userId = `user_${Math.floor(Math.random() * 10000)}`;
    let url = `${API_CONFIG.BASE_URL}?key=${apiKey}&task_type=${taskType}&dialect=en-us`;

    console.log("Starting PTE task analysis:", {
      taskType,
      originalAudioSize: `${(audioBuffer.length / 1024 / 1024).toFixed(2)}MB`,
      userId,
      contextProvided: !!context,
      contextLength: context?.length || 0,
    });

    // Optimize audio before sending
    const optimizedAudio = await optimizeAudioForApi(audioBuffer);

    const formData = new FormData();
    formData.append("user_audio_file", optimizedAudio, {
      filename: "recording.wav",
      contentType: "audio/wav",
    });

    // Add task-specific context
    if (context && context.trim()) {
      const contextData = {};

      switch (taskType) {
        case "describe-image":
          contextData.image_description = context.trim();
          break;
        case "retell-lecture":
          contextData.lecture_text = context.trim();
          break;
        case "answer-question":
          contextData.question = context.trim();
          break;
      }

      formData.append("task_context", JSON.stringify(contextData));
      console.log(`Added ${taskType} context:`, contextData);
    }

    console.log("Making request to SpeechAce API for PTE task:", {
      optimizedAudioSize: `${(optimizedAudio.length / 1024 / 1024).toFixed(
        2
      )}MB`,
      compressionRatio: `${(
        (1 - optimizedAudio.length / audioBuffer.length) *
        100
      ).toFixed(1)}%`,
    });

    // Use enhanced retry logic
    const response = await makeApiRequestWithRetry(url, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: "application/json",
      },
      maxBodyLength: Infinity,
    });

    if (response.data.status === "error") {
      throw new Error(
        response.data.detail_message ||
          response.data.short_message ||
          "SpeechAce API returned an error"
      );
    }

    console.log("SpeechAce API Response received successfully for PTE task");

    const transcript = response.data.speech_score?.transcript || "";

    // Handle empty transcript
    if (!transcript || transcript.trim().length === 0) {
      console.warn("Empty transcript received from SpeechAce API for PTE task");
      response.data.content = {
        score: 0,
        transcript: "",
        keywords: context ? context.split(",").map((k) => k.trim()) : [],
        similarity_score: 0,
      };
      return response.data;
    }

    // Calculate content score based on task type
    let contentScore = 0;
    let similarityScore = 0;
    let keywords = [];

    if (context && context.trim()) {
      keywords = context
        .split(",")
        .map((keyword) => keyword.trim().toLowerCase())
        .filter((k) => k.length > 0);

      if (keywords.length > 0) {
        const transcript_lower = transcript.toLowerCase();

        // Check for keyword matches in transcript
        const matchedKeywords = keywords.filter((keyword) =>
          transcript_lower.includes(keyword)
        );

        // Calculate keyword match percentage
        const keywordMatchPercentage = matchedKeywords.length / keywords.length;

        // Calculate content score based on keyword matches (scale to 90)
        contentScore = Math.round(keywordMatchPercentage * 90);

        // Calculate similarity using vectors
        const transcriptVector = getVector(transcript_lower.split(/\s+/));
        const keywordsVector = getVector(keywords);
        similarityScore = cosineSimilarity(transcriptVector, keywordsVector);

        console.log("PTE task analysis completed:", {
          taskType,
          matchedKeywords: matchedKeywords.length,
          totalKeywords: keywords.length,
          contentScore,
          similarityScore: similarityScore.toFixed(3),
        });
      }
    }

    // Create the content response
    response.data.content = {
      score: contentScore,
      transcript: transcript,
      provided_text: context || "",
      similarity_score: similarityScore,
      keywords: keywords,
    };

    return response.data;
  } catch (error) {
    console.error("PTE task analysis error:", {
      taskType,
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });

    // Provide specific error messages
    if (error.code === "ECONNABORTED") {
      throw new Error(
        "The PTE task analysis timed out. Try a shorter recording or check your internet connection."
      );
    }

    if (error.message.includes("network") || error.code === "ENOTFOUND") {
      throw new Error(
        "Network error occurred during PTE task analysis. Please check your internet connection and try again."
      );
    }

    if (error.response?.status === 413) {
      throw new Error(
        "Audio file is too large for PTE task analysis. Please record a shorter audio."
      );
    }

    if (error.response?.status === 429) {
      throw new Error(
        "Too many requests for PTE task analysis. Please wait a moment before trying again."
      );
    }

    // Re-throw with original message if no specific handling
    throw error;
  }
};
